<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件上传删除功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 25px 80px rgba(0,0,0,0.3);
        }

        .test-title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 24px;
            font-weight: 700;
        }

        .upload-section {
            border: 2px dashed #667eea;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
            background: rgba(102, 126, 234, 0.05);
        }

        .upload-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(102, 126, 234, 0.3);
        }

        .file-input {
            display: none;
        }

        .uploaded-files-preview {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 20px;
            min-height: 50px;
            padding: 15px;
            background: #f8f9ff;
            border-radius: 10px;
            border: 1px solid #e1e8f0;
        }

        .file-preview-item {
            display: flex;
            align-items: center;
            background: rgba(102, 126, 234, 0.1);
            border: 1px solid rgba(102, 126, 234, 0.2);
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 12px;
            color: rgba(102, 126, 234, 0.8);
            position: relative;
            max-width: 200px;
            transition: all 0.3s ease;
        }

        .file-preview-item:hover {
            background: rgba(102, 126, 234, 0.15);
            transform: translateY(-1px);
        }

        .file-preview-item .file-icon {
            margin-right: 6px;
            font-size: 14px;
        }

        .file-preview-item .file-name {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .file-preview-item .file-remove {
            margin-left: 6px;
            cursor: pointer;
            color: rgba(255, 0, 0, 0.6);
            font-weight: bold;
            font-size: 16px;
            line-height: 1;
            padding: 2px 4px;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .file-preview-item .file-remove:hover {
            color: rgba(255, 0, 0, 0.8);
            background: rgba(255, 0, 0, 0.1);
            transform: scale(1.2);
        }

        .file-processing {
            opacity: 0.6;
            pointer-events: none;
        }

        .debug-info {
            background: #f0f4f8;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 12px;
        }

        .debug-info h4 {
            margin-top: 0;
            color: #333;
        }

        .debug-log {
            background: white;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            max-height: 200px;
            overflow-y: auto;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
            border-bottom: 1px solid #eee;
        }

        .log-time {
            color: #666;
            font-size: 10px;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4CAF50;
            color: white;
            padding: 12px 20px;
            border-radius: 5px;
            display: none;
            z-index: 10000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .empty-state {
            text-align: center;
            color: #999;
            font-style: italic;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🗂️ 文件上传删除功能测试</h1>
        
        <div class="upload-section">
            <p>点击下方按钮选择文件进行测试</p>
            <button class="upload-btn" onclick="document.getElementById('testFileInput').click()">
                📎 选择文件
            </button>
            <input type="file" id="testFileInput" class="file-input" accept="image/*,video/*,.doc,.docx,.pdf,.xls,.xlsx,.txt" multiple>
        </div>

        <div class="uploaded-files-preview" id="uploadedFilesPreview">
            <div class="empty-state">暂无上传文件</div>
        </div>

        <div class="debug-info">
            <h4>🐛 调试信息</h4>
            <div>当前文件数量: <span id="fileCount">0</span></div>
            <div class="debug-log" id="debugLog"></div>
            <button onclick="clearDebugLog()" style="margin-top: 10px; padding: 5px 10px; background: #667eea; color: white; border: none; border-radius: 3px; cursor: pointer;">清除日志</button>
        </div>
    </div>

    <div class="notification" id="notification"></div>

    <script>
        let uploadedFiles = [];

        function debugLog(message) {
            const debugLogDiv = document.getElementById('debugLog');
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.innerHTML = `<span class="log-time">${new Date().toLocaleTimeString()}</span>: ${message}`;
            debugLogDiv.appendChild(logEntry);
            debugLogDiv.scrollTop = debugLogDiv.scrollHeight;
            console.log('DEBUG:', message);
        }

        function clearDebugLog() {
            document.getElementById('debugLog').innerHTML = '';
        }

        function showNotification(message) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.style.display = 'block';
            setTimeout(() => {
                notification.style.display = 'none';
            }, 3000);
        }

        function updateFileCount() {
            document.getElementById('fileCount').textContent = uploadedFiles.length;
            
            const preview = document.getElementById('uploadedFilesPreview');
            const emptyState = preview.querySelector('.empty-state');
            
            if (uploadedFiles.length === 0) {
                if (!emptyState) {
                    const emptyDiv = document.createElement('div');
                    emptyDiv.className = 'empty-state';
                    emptyDiv.textContent = '暂无上传文件';
                    preview.appendChild(emptyDiv);
                }
            } else {
                if (emptyState) {
                    emptyState.remove();
                }
            }
        }

        function getFileIcon(fileType, fileName) {
            if (fileType.startsWith('image/')) return '🖼️';
            if (fileType.startsWith('video/')) return '🎥';
            if (fileType.includes('pdf')) return '📄';
            if (fileType.includes('word') || fileName.endsWith('.doc') || fileName.endsWith('.docx')) return '📝';
            if (fileType.includes('excel') || fileName.endsWith('.xls') || fileName.endsWith('.xlsx')) return '📊';
            if (fileType.startsWith('text/')) return '📃';
            return '📎';
        }

        function renderFilePreview(fileInfo) {
            const filesPreview = document.getElementById('uploadedFilesPreview');
            if (!filesPreview) return;

            const fileItem = document.createElement('div');
            fileItem.className = 'file-preview-item';
            fileItem.setAttribute('data-file-id', fileInfo.id);

            const icon = getFileIcon(fileInfo.type, fileInfo.name);

            // 创建文件图标
            const fileIconSpan = document.createElement('span');
            fileIconSpan.className = 'file-icon';
            fileIconSpan.textContent = icon;

            // 创建文件名
            const fileNameSpan = document.createElement('span');
            fileNameSpan.className = 'file-name';
            fileNameSpan.title = fileInfo.name;
            fileNameSpan.textContent = fileInfo.name;

            // 创建删除按钮
            const removeSpan = document.createElement('span');
            removeSpan.className = 'file-remove';
            removeSpan.textContent = '×';
            removeSpan.title = '删除文件';
            
            // 使用事件监听器
            removeSpan.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                debugLog(`点击删除按钮，文件ID: ${fileInfo.id}`);
                removeUploadedFile(fileInfo.id);
            });

            // 组装元素
            fileItem.appendChild(fileIconSpan);
            fileItem.appendChild(fileNameSpan);
            fileItem.appendChild(removeSpan);

            filesPreview.appendChild(fileItem);
            debugLog(`已渲染文件预览: ${fileInfo.name}`);
        }

        function removeUploadedFile(fileId) {
            debugLog(`开始删除文件: ${fileId}`);
            
            // 从数组中移除文件
            const originalLength = uploadedFiles.length;
            uploadedFiles = uploadedFiles.filter(file => file.id !== fileId);
            
            debugLog(`文件数组长度从 ${originalLength} 变为 ${uploadedFiles.length}`);
            
            // 从DOM中移除文件预览元素
            const fileItem = document.querySelector(`[data-file-id="${fileId}"]`);
            if (fileItem) {
                fileItem.remove();
                debugLog('已从DOM中移除文件预览元素');
                showNotification('文件已删除');
                updateFileCount();
            } else {
                debugLog('未找到要删除的文件预览元素');
            }
        }

        function handleFileUpload(event) {
            const files = Array.from(event.target.files);
            if (files.length === 0) return;

            debugLog(`选择了 ${files.length} 个文件`);

            for (const file of files) {
                // 检查文件大小（限制为10MB）
                if (file.size > 10 * 1024 * 1024) {
                    debugLog(`文件 ${file.name} 超过10MB限制，已跳过`);
                    showNotification(`文件 ${file.name} 超过10MB限制，已跳过`);
                    continue;
                }

                const fileId = Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                const fileInfo = {
                    id: fileId,
                    name: file.name,
                    type: file.type,
                    size: file.size,
                    file: file
                };

                uploadedFiles.push(fileInfo);
                renderFilePreview(fileInfo);
                debugLog(`已添加文件: ${file.name}, ID: ${fileId}`);
            }

            updateFileCount();
            // 清空文件输入
            event.target.value = '';
        }

        // 事件监听
        document.getElementById('testFileInput').addEventListener('change', handleFileUpload);

        // 初始化
        debugLog('文件上传删除功能测试页面已加载');
        updateFileCount();
    </script>
</body>
</html>
