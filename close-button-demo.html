<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关闭按钮居中优化演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 30px;
        }

        .demo-title {
            color: white;
            font-size: 28px;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            margin-bottom: 20px;
        }

        .comparison-container {
            display: flex;
            gap: 40px;
            align-items: center;
        }

        .button-demo {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
        }

        .demo-label {
            color: white;
            font-size: 16px;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }

        /* 优化前的按钮样式 */
        .close-btn-before {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            font-size: 20px; /* 原来的大字体 */
            cursor: pointer;
            color: white;
            line-height: 1.2; /* 原来可能有的行高问题 */
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            /* 缺少一些居中属性 */
        }

        /* 优化后的按钮样式 */
        .close-btn-after {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            font-size: 18px; /* 稍微减小字体以确保居中 */
            cursor: pointer;
            color: white;
            line-height: 1; /* 确保行高为1 */
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            font-family: Arial, sans-serif; /* 使用标准字体确保×符号居中 */
            font-weight: normal; /* 确保字体粗细正常 */
            padding: 0; /* 移除任何默认内边距 */
            margin: 0; /* 移除任何默认外边距 */
            text-align: center; /* 文本居中 */
            vertical-align: middle; /* 垂直居中 */
        }

        .close-btn-before:hover,
        .close-btn-after:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .improvement-list {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            color: white;
            max-width: 600px;
            margin-top: 20px;
        }

        .improvement-list h3 {
            margin-top: 0;
            font-size: 20px;
            margin-bottom: 15px;
        }

        .improvement-list ul {
            list-style: none;
            padding: 0;
        }

        .improvement-list li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            padding-left: 25px;
        }

        .improvement-list li:last-child {
            border-bottom: none;
        }

        .improvement-list li::before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #4CAF50;
            font-weight: bold;
            font-size: 16px;
        }

        .code-example {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 13px;
            color: #E8E8E8;
            overflow-x: auto;
        }

        .highlight {
            background: rgba(255, 255, 255, 0.15);
            padding: 2px 6px;
            border-radius: 4px;
            color: #FFD700;
        }

        .arrow {
            font-size: 30px;
            color: white;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
    </style>
</head>
<body>
    <div class="demo-title">🎯 关闭按钮居中优化演示</div>
    
    <div class="comparison-container">
        <div class="button-demo">
            <div class="demo-label">优化前</div>
            <button class="close-btn-before">&times;</button>
            <div style="color: rgba(255,255,255,0.8); font-size: 12px; text-align: center;">
                字体较大，可能偏移
            </div>
        </div>
        
        <div class="arrow">→</div>
        
        <div class="button-demo">
            <div class="demo-label">优化后</div>
            <button class="close-btn-after">&times;</button>
            <div style="color: rgba(255,255,255,0.8); font-size: 12px; text-align: center;">
                完美居中显示
            </div>
        </div>
    </div>

    <div class="improvement-list">
        <h3>🔧 优化细节</h3>
        <ul>
            <li>调整字体大小从 <span class="highlight">20px</span> 到 <span class="highlight">18px</span>，确保符号不会超出按钮边界</li>
            <li>设置 <span class="highlight">line-height: 1</span> 消除行高带来的垂直偏移</li>
            <li>使用 <span class="highlight">font-family: Arial, sans-serif</span> 确保×符号在不同系统下显示一致</li>
            <li>添加 <span class="highlight">padding: 0; margin: 0</span> 移除浏览器默认样式影响</li>
            <li>设置 <span class="highlight">font-weight: normal</span> 确保字体粗细一致</li>
            <li>使用 <span class="highlight">text-align: center</span> 和 <span class="highlight">vertical-align: middle</span> 双重保险</li>
        </ul>
    </div>

    <div class="improvement-list">
        <h3>💻 关键CSS代码</h3>
        <div class="code-example">
#closeTrainingDialogBtn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    <span class="highlight">font-size: 18px;</span> /* 稍微减小字体以确保居中 */
    cursor: pointer;
    color: white;
    <span class="highlight">line-height: 1;</span> /* 确保行高为1 */
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    <span class="highlight">font-family: Arial, sans-serif;</span> /* 标准字体 */
    <span class="highlight">font-weight: normal;</span> /* 字体粗细正常 */
    <span class="highlight">padding: 0;</span> /* 移除默认内边距 */
    <span class="highlight">margin: 0;</span> /* 移除默认外边距 */
    <span class="highlight">text-align: center;</span> /* 文本居中 */
    <span class="highlight">vertical-align: middle;</span> /* 垂直居中 */
}
        </div>
    </div>

    <div class="improvement-list">
        <h3>🎨 视觉效果提升</h3>
        <ul>
            <li>×符号现在完美居中显示在圆形按钮内</li>
            <li>在不同浏览器和操作系统下保持一致的显示效果</li>
            <li>悬停效果更加流畅和自然</li>
            <li>整体视觉平衡感更好</li>
        </ul>
    </div>

    <script>
        // 添加一些交互效果
        document.querySelectorAll('.close-btn-before, .close-btn-after').forEach(btn => {
            btn.addEventListener('click', function() {
                this.style.transform = 'scale(0.9)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    </script>
</body>
</html>
