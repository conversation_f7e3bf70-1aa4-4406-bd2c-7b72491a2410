<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能体训练搜索功能演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        /* 复制优化后的样式 */
        .ai-training-dialog {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 900px;
            height: 650px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 20px;
            box-shadow: 0 25px 80px rgba(0,0,0,0.3), 0 0 0 1px rgba(255,255,255,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            backdrop-filter: blur(20px);
        }

        .ai-training-dialog-header {
            padding: 20px 25px;
            background: rgba(255, 255, 255, 0.15);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            border-top-left-radius: 20px;
            border-top-right-radius: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 18px;
            font-weight: 700;
            color: white;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .header-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .header-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: white;
            line-height: 1;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            font-family: Arial, sans-serif;
            font-weight: normal;
            padding: 0;
            margin: 0;
            text-align: center;
            vertical-align: middle;
        }

        .header-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        /* 搜索栏样式 */
        .search-bar {
            background: rgba(255, 255, 255, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 15px 25px;
            backdrop-filter: blur(10px);
            animation: slideDown 0.3s ease;
        }

        .search-container {
            display: flex;
            align-items: center;
            gap: 10px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 25px;
            padding: 8px 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        #searchInput {
            flex: 1;
            border: none;
            background: transparent;
            outline: none;
            font-size: 14px;
            color: #333;
            padding: 5px 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }

        #searchInput::placeholder {
            color: #999;
        }

        .search-controls {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .search-controls button {
            background: rgba(102, 126, 234, 0.1);
            border: 1px solid rgba(102, 126, 234, 0.2);
            border-radius: 4px;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 12px;
            color: rgba(102, 126, 234, 0.8);
            transition: all 0.2s ease;
        }

        .search-controls button:hover {
            background: rgba(102, 126, 234, 0.2);
            border-color: rgba(102, 126, 234, 0.4);
        }

        .search-controls button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        #searchResults {
            font-size: 12px;
            color: #666;
            min-width: 30px;
            text-align: center;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 搜索高亮样式 */
        .search-highlight {
            background: #ffeb3b !important;
            color: #333 !important;
            padding: 2px 4px;
            border-radius: 3px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }

        .search-highlight.current {
            background: #ff9800 !important;
            color: white !important;
        }

        .ai-training-dialog-body {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .ai-training-messages {
            flex-grow: 1;
            overflow-y: auto;
            padding: 25px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            margin: 0;
        }

        .ai-training-msg {
            display: flex;
            margin-bottom: 20px;
            max-width: 80%;
            animation: fadeInUp 0.3s ease;
            align-items: flex-end;
        }
        .ai-training-msg.user {
            margin-left: auto;
            flex-direction: row-reverse;
        }
        .ai-training-msg .avatar {
            width: 42px;
            height: 42px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            text-align: center;
            line-height: 42px;
            font-weight: 600;
            flex-shrink: 0;
            color: white;
            font-size: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            margin-bottom: 5px;
        }
        .ai-training-msg.user .avatar {
            background: linear-gradient(135deg, #1E90FF 0%, #00BFFF 100%);
        }
        .ai-training-msg.bot .avatar {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        .ai-training-msg .content {
            margin: 0 15px;
            padding: 15px 20px;
            border-radius: 18px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            font-size: 14px;
            word-wrap: break-word;
            white-space: pre-wrap;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: relative;
            line-height: 1.5;
        }
        .ai-training-msg.user .content {
            background: linear-gradient(135deg, #1E90FF 0%, #00BFFF 100%);
            color: white;
        }
        .ai-training-msg.user .content::after {
            content: '';
            position: absolute;
            right: -8px;
            bottom: 15px;
            width: 0;
            height: 0;
            border-left: 8px solid #1E90FF;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
        }
        .ai-training-msg.bot .content {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border: 1px solid rgba(0,0,0,0.05);
        }
        .ai-training-msg.bot .content::before {
            content: '';
            position: absolute;
            left: -8px;
            bottom: 15px;
            width: 0;
            height: 0;
            border-right: 8px solid #ffffff;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .demo-title {
            text-align: center;
            color: white;
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 30px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .demo-instructions {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 10px;
            padding: 15px;
            color: white;
            font-size: 14px;
            max-width: 300px;
        }

        .demo-instructions h4 {
            margin-top: 0;
            margin-bottom: 10px;
        }

        .demo-instructions ul {
            margin: 0;
            padding-left: 20px;
        }

        .demo-instructions li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="demo-title">🔍 AI智能体训练搜索功能演示</div>
    
    <div class="ai-training-dialog">
        <div class="ai-training-dialog-header">
            <span>AI 智能体训练</span>
            <div class="header-controls">
                <button class="header-btn" onclick="toggleSearch()" title="搜索对话内容">🔍</button>
                <button class="header-btn" title="关闭">&times;</button>
            </div>
        </div>
        <div class="search-bar" id="searchBar" style="display: none;">
            <div class="search-container">
                <input type="text" id="searchInput" placeholder="搜索对话内容..." />
                <div class="search-controls">
                    <button onclick="searchPrev()" title="上一个">↑</button>
                    <button onclick="searchNext()" title="下一个">↓</button>
                    <span id="searchResults">0/0</span>
                    <button onclick="closeSearch()" title="关闭搜索">&times;</button>
                </div>
            </div>
        </div>
        <div class="ai-training-dialog-body">
            <div class="ai-training-messages" id="messages">
                <div class="ai-training-msg user">
                    <div class="avatar">你</div>
                    <div class="content">你好，我想学习一些客服知识</div>
                </div>
                <div class="ai-training-msg bot">
                    <div class="avatar">AI</div>
                    <div class="content">您好！我很乐意帮助您学习客服知识。您可以通过文字描述或上传文件的方式来训练我，我会认真学习并记住这些知识。</div>
                </div>
                <div class="ai-training-msg user">
                    <div class="avatar">你</div>
                    <div class="content">如何处理客户投诉？</div>
                </div>
                <div class="ai-training-msg bot">
                    <div class="avatar">AI</div>
                    <div class="content">处理客户投诉的关键步骤：1. 耐心倾听客户的问题；2. 表示理解和同情；3. 快速找到解决方案；4. 跟进确保客户满意。记住，客户投诉是改进服务的机会。</div>
                </div>
                <div class="ai-training-msg user">
                    <div class="avatar">你</div>
                    <div class="content">搜索功能很实用！</div>
                </div>
                <div class="ai-training-msg bot">
                    <div class="avatar">AI</div>
                    <div class="content">是的！新的搜索功能可以帮助您快速找到对话中的特定内容。您可以搜索关键词，系统会自动高亮显示所有匹配的结果，并支持上下导航。</div>
                </div>
            </div>
        </div>
    </div>

    <div class="demo-instructions">
        <h4>🎯 使用说明</h4>
        <ul>
            <li>点击🔍按钮打开搜索栏</li>
            <li>输入关键词进行搜索</li>
            <li>使用↑↓按钮或Enter键导航</li>
            <li>当前结果会用橙色高亮</li>
            <li>按Esc键关闭搜索</li>
        </ul>
    </div>

    <script>
        let searchResults = [];
        let currentIndex = -1;

        function toggleSearch() {
            const searchBar = document.getElementById('searchBar');
            const searchInput = document.getElementById('searchInput');
            
            if (searchBar.style.display === 'none') {
                searchBar.style.display = 'block';
                searchInput.focus();
            } else {
                closeSearch();
            }
        }

        function closeSearch() {
            const searchBar = document.getElementById('searchBar');
            const searchInput = document.getElementById('searchInput');
            
            searchBar.style.display = 'none';
            searchInput.value = '';
            clearHighlights();
            updateResults();
        }

        function performSearch(query) {
            clearHighlights();
            searchResults = [];
            currentIndex = -1;

            if (!query) {
                updateResults();
                return;
            }

            const messages = document.querySelectorAll('.ai-training-msg .content');
            messages.forEach((msg, msgIndex) => {
                const text = msg.textContent;
                const regex = new RegExp(escapeRegExp(query), 'gi');
                let match;
                let html = text;
                let offset = 0;

                while ((match = regex.exec(text)) !== null) {
                    const start = match.index + offset;
                    const end = start + query.length;
                    const before = html.substring(0, start);
                    const matchText = html.substring(start, end);
                    const after = html.substring(end);
                    
                    const id = `highlight-${searchResults.length}`;
                    html = before + `<span class="search-highlight" id="${id}">${matchText}</span>` + after;
                    
                    searchResults.push({ element: msg, id: id });
                    offset += `<span class="search-highlight" id="${id}"></span>`.length - query.length;
                    regex.lastIndex = match.index + 1;
                }
                
                if (html !== text) {
                    msg.innerHTML = html;
                }
            });

            updateResults();
            if (searchResults.length > 0) {
                currentIndex = 0;
                highlightCurrent();
            }
        }

        function searchNext() {
            if (searchResults.length === 0) return;
            currentIndex = (currentIndex + 1) % searchResults.length;
            highlightCurrent();
        }

        function searchPrev() {
            if (searchResults.length === 0) return;
            currentIndex = currentIndex <= 0 ? searchResults.length - 1 : currentIndex - 1;
            highlightCurrent();
        }

        function highlightCurrent() {
            document.querySelectorAll('.search-highlight.current').forEach(el => {
                el.classList.remove('current');
            });

            if (currentIndex >= 0 && currentIndex < searchResults.length) {
                const current = document.getElementById(searchResults[currentIndex].id);
                if (current) {
                    current.classList.add('current');
                    current.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            }
            updateResults();
        }

        function clearHighlights() {
            document.querySelectorAll('.ai-training-msg .content').forEach(msg => {
                const highlights = msg.querySelectorAll('.search-highlight');
                highlights.forEach(highlight => {
                    msg.replaceChild(document.createTextNode(highlight.textContent), highlight);
                });
                msg.normalize();
            });
        }

        function updateResults() {
            const resultsSpan = document.getElementById('searchResults');
            if (searchResults.length === 0) {
                resultsSpan.textContent = '0/0';
            } else {
                resultsSpan.textContent = `${currentIndex + 1}/${searchResults.length}`;
            }
        }

        function escapeRegExp(string) {
            return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        }

        // 事件监听
        document.getElementById('searchInput').addEventListener('input', function(e) {
            performSearch(e.target.value.trim());
        });

        document.getElementById('searchInput').addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                if (e.shiftKey) {
                    searchPrev();
                } else {
                    searchNext();
                }
            } else if (e.key === 'Escape') {
                closeSearch();
            }
        });
    </script>
</body>
</html>
