<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能体训练UI优化演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        /* 复制优化后的样式 */
        .ai-training-dialog {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 900px;
            height: 650px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 20px;
            box-shadow: 0 25px 80px rgba(0,0,0,0.3), 0 0 0 1px rgba(255,255,255,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            backdrop-filter: blur(20px);
        }

        .ai-training-dialog-header {
            padding: 20px 25px;
            background: rgba(255, 255, 255, 0.15);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            border-top-left-radius: 20px;
            border-top-right-radius: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 18px;
            font-weight: 700;
            color: white;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .ai-training-dialog-body {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .ai-training-messages {
            flex-grow: 1;
            overflow-y: auto;
            padding: 25px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            margin: 0;
        }

        .ai-training-msg {
            display: flex;
            margin-bottom: 20px;
            max-width: 80%;
            animation: fadeInUp 0.3s ease;
            align-items: flex-end;
        }
        .ai-training-msg.user {
            margin-left: auto;
            flex-direction: row-reverse;
        }
        .ai-training-msg .avatar {
            width: 42px;
            height: 42px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            text-align: center;
            line-height: 42px;
            font-weight: 600;
            flex-shrink: 0;
            color: white;
            font-size: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            margin-bottom: 5px;
        }
        .ai-training-msg.user .avatar {
            background: linear-gradient(135deg, #1E90FF 0%, #00BFFF 100%);
        }
        .ai-training-msg.bot .avatar {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        .ai-training-msg .content {
            margin: 0 15px;
            padding: 15px 20px;
            border-radius: 18px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            font-size: 14px;
            word-wrap: break-word;
            white-space: pre-wrap;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: relative;
            line-height: 1.5;
        }
        .ai-training-msg.user .content {
            background: linear-gradient(135deg, #1E90FF 0%, #00BFFF 100%);
            color: white;
        }
        .ai-training-msg.user .content::after {
            content: '';
            position: absolute;
            right: -8px;
            bottom: 15px;
            width: 0;
            height: 0;
            border-left: 8px solid #1E90FF;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
        }
        .ai-training-msg.bot .content {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border: 1px solid rgba(0,0,0,0.05);
        }
        .ai-training-msg.bot .content::before {
            content: '';
            position: absolute;
            left: -8px;
            bottom: 15px;
            width: 0;
            height: 0;
            border-right: 8px solid #ffffff;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
        }

        .ai-training-input-area {
            padding: 25px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            position: relative;
        }
        .ai-training-input-container {
            position: relative;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .upload-file-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid rgba(102, 126, 234, 0.3);
            background: rgba(255, 255, 255, 0.9);
            color: rgba(102, 126, 234, 0.8);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            font-size: 18px;
            font-weight: bold;
            backdrop-filter: blur(10px);
            flex-shrink: 0;
            margin-bottom: 5px;
        }
        .ai-training-input-area textarea {
            flex-grow: 1;
            padding: 15px 20px;
            border: 2px solid rgba(102, 126, 234, 0.2);
            border-radius: 25px;
            resize: none;
            font-size: 14px;
            height: 50px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            outline: none;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            line-height: 1.4;
        }
        .send-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: none;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
            font-size: 16px;
            flex-shrink: 0;
            margin-bottom: 5px;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .demo-title {
            text-align: center;
            color: white;
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 30px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
    </style>
</head>
<body>
    <div class="demo-title">🎨 AI智能体训练UI优化演示</div>
    
    <div class="ai-training-dialog">
        <div class="ai-training-dialog-header">
            <span>AI 智能体训练</span>
            <button style="background: rgba(255, 255, 255, 0.2); border: none; font-size: 20px; color: white; width: 32px; height: 32px; border-radius: 50%; cursor: pointer;">&times;</button>
        </div>
        <div class="ai-training-dialog-body">
            <div class="ai-training-messages">
                <div class="ai-training-msg user">
                    <div class="avatar">你</div>
                    <div class="content">你好，我想学习一些客服知识</div>
                </div>
                <div class="ai-training-msg bot">
                    <div class="avatar">AI</div>
                    <div class="content">您好！我很乐意帮助您学习客服知识。您可以通过文字描述或上传文件的方式来训练我，我会认真学习并记住这些知识。</div>
                </div>
                <div class="ai-training-msg user">
                    <div class="avatar">你</div>
                    <div class="content">现在的颜色看起来清晰多了！</div>
                </div>
                <div class="ai-training-msg bot">
                    <div class="avatar">AI</div>
                    <div class="content">是的！现在用户消息使用了更清晰的蓝色渐变，文字更容易阅读。同时每个气泡都添加了指向头像的三角形箭头，看起来更像真实的对话气泡。</div>
                </div>
            </div>
            <div class="ai-training-input-area">
                <div class="ai-training-input-container">
                    <button class="upload-file-btn" title="上传文件">+</button>
                    <textarea placeholder="在此输入内容或上传文件进行训练"></textarea>
                    <button class="send-btn">➤</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
